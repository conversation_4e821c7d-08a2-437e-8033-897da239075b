# إعداد Firebase للعمل مع Play Store

## 🎯 الهدف
إعداد Google Sign-In للعمل مع المستخدمين الذين يحملون التطبيق من Play Store.

## 📱 SHA-1 Fingerprints المطلوبة

### من Play Store Console:
- **Play Store SHA-1**: `3C:FD:A5:8A:3B:31:26:2B:BD:C1:AF:26:B8:5F:C2:84:DA:66:B4:67`

### من التطوير المحلي:
- **Debug SHA-1**: `8A:C7:3C:0C:FF:C2:1C:28:03:84:97:FB:8E:33:F3:7B:1D:55:D5:23`
- **Release SHA-1**: `A8:1E:FC:57:F5:B0:31:19:96:39:60:40:BD:FE:A3:7C:CD:04:CD:8E`

## 🔧 خطوات الإعداد في Firebase Console

### 1. الدخول إلى Firebase Console
1. اذهب إلى [Firebase Console](https://console.firebase.google.com)
2. اختر مشروع `test-5c820`

### 2. إضافة SHA-1 Fingerprints
1. اذهب إلى **Project Settings** (⚙️)
2. في تبويب **"Your apps"**
3. اختر Android app (`com.busaty.school`)
4. في قسم **"SHA certificate fingerprints"**
5. أضف SHA-1 الجديد: `3C:FD:A5:8A:3B:31:26:2B:BD:C1:AF:26:B8:5F:C2:84:DA:66:B4:67`
6. اضغط **"Add fingerprint"**

### 3. تفعيل Google Sign-In
1. اذهب إلى **Authentication**
2. في تبويب **"Sign-in method"**
3. تأكد من تفعيل **Google**
4. تأكد من إضافة **Support email**

### 4. تحميل google-services.json الجديد
1. في **Project Settings** > **Your apps**
2. اضغط على **"Download google-services.json"**
3. استبدل الملف في `android/app/google-services.json`

## 📋 Client IDs المتوقعة

بعد إضافة SHA-1 الجديد، ستحصل على Client ID جديد من Firebase Console:

```
Debug: 545165014521-1393tegfefvphst3gnurfepdtte35920.apps.googleusercontent.com
Release/Play Store: 545165014521-[new-client-id].apps.googleusercontent.com
```

## ⚠️ ملاحظات مهمة

1. **انتظار التحديث**: قد يستغرق تحديث Firebase Console بضع دقائق
2. **اختبار التطبيق**: اختبر Google Sign-In في كل من:
   - Debug build (التطوير)
   - Release build (الإنتاج)
   - Play Store version (النسخة المنشورة)

3. **تحديث Client ID**: بعد الحصول على Client ID الجديد من Firebase، حدث:
   - `lib/config/google_signin_config.dart`
   - `android/app/google-services.json`

## 🧪 اختبار الإعداد

### للتطوير (Debug):
```bash
flutter run --debug
```

### للإنتاج (Release):
```bash
flutter build apk --release
flutter install --release
```

### للـ Play Store:
- ارفع APK/AAB إلى Play Store
- اختبر التطبيق من Play Store

## 🔍 استكشاف الأخطاء

إذا لم يعمل Google Sign-In:

1. **تحقق من SHA-1**: تأكد من إضافة جميع SHA-1 fingerprints
2. **تحقق من Package Name**: `com.busaty.school`
3. **تحقق من Client IDs**: في Firebase Console
4. **انتظر التحديث**: قد يستغرق 5-10 دقائق
5. **امسح cache التطبيق**: في الجهاز

## 📞 الدعم

إذا واجهت مشاكل، تحقق من:
- Firebase Console logs
- Android Studio logs
- Play Console error reports
