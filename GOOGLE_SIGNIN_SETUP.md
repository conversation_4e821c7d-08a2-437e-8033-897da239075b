# Google Sign-In Setup Guide

## Current Configuration

تم تكوين Google Sign-In لاستخدام الـ Client ID التالي:

### Android
- **Client ID**: `************-1393tegfefvphst3gnurfepdtte35920.apps.googleusercontent.com`
- **Project**: `test-5c820`
- **Package Name**: `com.busaty.school`

### iOS
- **Client ID**: `************-6lga42ejvda9e36l607gkllobevvpjfn.apps.googleusercontent.com`
- **Project**: `test-5c820` (موحد مع Android)
- **Bundle ID**: `com.busaty.school`

## الملفات المحدثة

### 1. Android Configuration
- ✅ `android/app/google-services.json` - يحتوي على الـ Client ID الصحيح
- ✅ `android/app/build.gradle` - تم إضافة REVERSED_CLIENT_ID

### 2. iOS Configuration
- ✅ `ios/Runner/Info.plist` - تم إضافة CFBundleURLTypes للـ URL scheme
- ✅ `ios/Runner/GoogleService-Info.plist` - موجود

### 3. Flutter Code
- ✅ `lib/config/google_signin_config.dart` - ملف تكوين جديد للـ Client IDs
- ✅ `lib/views/screens/login_screen/login_screen.dart` - تم تحديث الكود لاستخدام الـ Client ID الصحيح

## التحقق من الإعداد

### Android
1. تأكد من أن `google-services.json` يحتوي على الـ Client ID الصحيح
2. تأكد من أن SHA-1 fingerprint مطابق في Firebase Console
3. تأكد من أن package name صحيح: `com.busaty.school`

### iOS
1. تأكد من أن Bundle ID صحيح: `com.busaty.school`
2. تأكد من أن URL scheme مضاف في Info.plist
3. تأكد من أن GoogleService-Info.plist موجود

## مشاكل محتملة

### ✅ مشروع Firebase موحد
تم توحيد المشروع بنجاح:
- Android: `test-5c820` ✅
- iOS: `test-5c820` ✅
- Web: `test-5c820` ✅

جميع المنصات تستخدم نفس المشروع الآن.

### إذا لم يعمل Google Sign-In

1. **تحقق من SHA-1 fingerprint**:
   ```bash
   cd android
   ./gradlew signingReport
   ```

2. **تحقق من package name/bundle ID**:
   - Android: `com.busaty.school`
   - iOS: `com.busaty.school`

3. **تحقق من Firebase Console**:
   - تأكد من أن التطبيق مضاف للمشروع
   - تأكد من أن Google Sign-In مفعل

## الخطوات التالية

1. اختبار Google Sign-In على Android
2. إضافة iOS app للمشروع `test-5c820` أو نقل Android للمشروع `busaty-app`
3. تحديث Client ID للـ iOS في `google_signin_config.dart`
