# 🔍 تشخيص مشكلة Google Sign-In في Play Store

## 📱 الحالة الحالية
- ✅ SHA-1 من Play Store موجود في Firebase
- ✅ Client ID صحيح: `545165014521-q7v03d8o8qk4ugvk5tslb922ccfjjin5.apps.googleusercontent.com`
- ❌ Google Sign-In لا يعمل في التطبيق المحمل من Play Store

---

## 🔧 خطوات التشخيص

### 1. **تحقق من Firebase Console**

اذهب إلى [Firebase Console](https://console.firebase.google.com/project/test-5c820) وتأكد من:

#### Authentication Settings:
- [ ] **Authentication** → **Sign-in method** → **Google** (مفعل)
- [ ] **Support email** محدد
- [ ] **Project public-facing name** محدد

#### Project Settings:
- [ ] **Project Settings** → **General** → **Public settings**
- [ ] **Project public-facing name**: يجب أن يكون محدد
- [ ] **Support email**: يجب أن يكون محدد

### 2. **تحقق من Package Name**

تأكد أن Package Name متطابق في:
- [ ] **Play Store**: `com.busaty.school`
- [ ] **Firebase Console**: `com.busaty.school`
- [ ] **android/app/build.gradle**: `applicationId "com.busaty.school"`

### 3. **تحقق من SHA-1 في Firebase**

في Firebase Console → Project Settings → Your apps → Android:
- [ ] SHA-1 من Play Store موجود: `3C:FD:A5:8A:3B:31:26:2B:BD:C1:AF:26:B8:5F:C2:84:DA:66:B4:67`

---

## 🧪 اختبار مفصل

### الخطوة 1: تشغيل التطبيق مع Logs

```bash
flutter run --release
```

### الخطوة 2: مراقبة Logs

ابحث عن هذه الرسائل في logs:

```
=== Google Sign In Debug ===
Package Name: com.busaty.school
Debug Mode: false
Platform: TargetPlatform.android
GoogleSignIn instance created
Available accounts: false
Starting Google Sign In...
```

### الخطوة 3: تحليل الأخطاء

إذا ظهرت أخطاء، ابحث عن:

#### خطأ "DEVELOPER_ERROR":
```
PlatformException(sign_in_failed, com.google.android.gms.common.api.ApiException: 10: , null, null)
```
**الحل**: مشكلة في SHA-1 أو Client ID

#### خطأ "NETWORK_ERROR":
```
PlatformException(network_error, A network error occurred, null, null)
```
**الحل**: مشكلة في الاتصال

#### خطأ "SIGN_IN_CANCELLED":
```
Google Sign In was cancelled by user
```
**الحل**: المستخدم ألغى العملية

---

## 🔄 حلول محتملة

### الحل 1: إعادة تحميل google-services.json

1. اذهب إلى Firebase Console
2. Project Settings → Your apps → Android
3. اضغط **"Download google-services.json"**
4. استبدل الملف في `android/app/google-services.json`
5. أعد بناء التطبيق

### الحل 2: تنظيف وإعادة البناء

```bash
flutter clean
flutter pub get
flutter build apk --release
```

### الحل 3: التحقق من OAuth consent screen

في [Google Cloud Console](https://console.cloud.google.com):
1. اختر مشروع `test-5c820`
2. اذهب إلى **APIs & Services** → **OAuth consent screen**
3. تأكد من:
   - [ ] **App name** محدد
   - [ ] **User support email** محدد
   - [ ] **Developer contact information** محدد
   - [ ] **Publishing status**: Published (إذا كان التطبيق عام)

### الحل 4: إضافة SHA-1 إضافية

أحياناً Play Store يستخدم SHA-1 مختلفة. جرب إضافة هذه:

```
# SHA-1 من Play Console (الحالي)
3C:FD:A5:8A:3B:31:26:2B:BD:C1:AF:26:B8:5F:C2:84:DA:66:B4:67

# SHA-1 البديلة (جرب إضافتها)
DA:39:A3:EE:5E:6B:4B:0D:32:55:BF:EF:95:60:18:90:AF:D8:07:09
```

---

## 🚨 أخطاء شائعة

### 1. **OAuth consent screen غير مكتمل**
- تأكد من ملء جميع الحقول المطلوبة
- تأكد من نشر التطبيق (Published)

### 2. **Package Name مختلف**
- تحقق من Package Name في جميع الأماكن

### 3. **SHA-1 مفقود أو خاطئ**
- تأكد من نسخ SHA-1 الصحيح من Play Console

### 4. **Client ID خاطئ**
- تأكد من استخدام Client ID الصحيح للـ SHA-1

---

## 📞 الخطوات التالية

1. **جرب التطبيق مرة أخرى** مع مراقبة logs
2. **أرسل logs الأخطاء** إذا ظهرت
3. **تحقق من OAuth consent screen** في Google Cloud Console
4. **جرب إضافة SHA-1 إضافية** إذا لم تنجح الحلول الأخرى

---

## 📋 معلومات للمرجع

- **Firebase Project**: test-5c820
- **Package Name**: com.busaty.school
- **Play Store SHA-1**: 3C:FD:A5:8A:3B:31:26:2B:BD:C1:AF:26:B8:5F:C2:84:DA:66:B4:67
- **Client ID**: 545165014521-q7v03d8o8qk4ugvk5tslb922ccfjjin5.apps.googleusercontent.com
