import 'package:flutter/foundation.dart';

/// Configuration class for Google Sign-In Client IDs
/// All Client IDs are from test-5c820 Firebase project
class GoogleSignInConfig {
  // Client ID for Android (from test-5c820 project)
  static const String androidClientId =
      '545165014521-1393tegfefvphst3gnurfepdtte35920.apps.googleusercontent.com';

  // Client ID for iOS (from test-5c820 project)
  static const String iosClientId =
      '545165014521-6lga42ejvda9e36l607gkllobevvpjfn.apps.googleusercontent.com';

  // Client ID for Web (from test-5c820 project - update when web app is added)
  static const String webClientId =
      '545165014521-your-web-client-id.apps.googleusercontent.com';

  /// Get the appropriate client ID based on the current platform
  static String get currentPlatformClientId {
    if (kIsWeb) {
      return webClientId;
    }

    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return androidClientId;
      case TargetPlatform.iOS:
        return iosClientId;
      default:
        return androidClientId; // fallback
    }
  }
}
