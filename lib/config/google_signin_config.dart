import 'package:flutter/foundation.dart';

/// Configuration class for Google Sign-In Client IDs
class GoogleSignInConfig {
  // Client ID for Android (from test-5c820 project)
  static const String androidClientId = '545165014521-1393tegfefvphst3gnurfepdtte35920.apps.googleusercontent.com';
  
  // Client ID for iOS (from busaty-app project)
  static const String iosClientId = '66220404803-your-ios-client-id.apps.googleusercontent.com';
  
  // Client ID for Web (if needed)
  static const String webClientId = '66220404803-your-web-client-id.apps.googleusercontent.com';
  
  /// Get the appropriate client ID based on the current platform
  static String get currentPlatformClientId {
    if (kIsWeb) {
      return webClientId;
    }
    
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return androidClientId;
      case TargetPlatform.iOS:
        return iosClientId;
      default:
        return androidClientId; // fallback
    }
  }
}
