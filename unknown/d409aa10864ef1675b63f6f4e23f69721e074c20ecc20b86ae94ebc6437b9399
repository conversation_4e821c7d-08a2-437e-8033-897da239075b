# Firebase Certificate Keys Reference

## 🎯 مشروع Firebase: test-5c820
## 📱 Package Name: com.busaty.school

---

## 🔑 جميع المفاتيح المطلوبة لـ Firebase Console

### 📋 قائمة المفاتيح للنسخ المباشر

```
8A:C7:3C:0C:FF:C2:1C:28:03:84:97:FB:8E:33:F3:7B:1D:55:D5:23
C7:8D:1D:6D:5B:AB:85:17:15:4B:EE:F9:B4:7C:FD:E0:A9:CB:65:9C:67:22:AD:FB:63:75:76:73:0D:5D:CC:2C
A8:1E:FC:57:F5:B0:31:19:96:39:60:40:BD:FE:A3:7C:CD:04:CD:8E
BB:5E:C7:D4:D2:C5:10:8F:55:B2:EB:3E:10:3F:AB:7A:18:28:15:92:1F:B9:C9:02:6E:98:91:FB:F3:55:F7:87
3C:FD:A5:8A:3B:31:26:2B:BD:C1:AF:26:B8:5F:C2:84:DA:66:B4:67
4D:81:30:A9:D7:80:18:D9:09:68:9D:7C:B6:8F:16:A6:96:20:B8:50:8D:C8:C8:E1:83:5D:66:84:A2:5E:FE:67
```

---

## 📊 تفصيل المفاتيح حسب البيئة

### 🛠️ Debug Environment (التطوير)
**المصدر**: Local development keystore
**الاستخدام**: flutter run, Android Studio debugging

```
SHA-1:   8A:C7:3C:0C:FF:C2:1C:28:03:84:97:FB:8E:33:F3:7B:1D:55:D5:23
SHA-256: C7:8D:1D:6D:5B:AB:85:17:15:4B:EE:F9:B4:7C:FD:E0:A9:CB:65:9C:67:22:AD:FB:63:75:76:73:0D:5D:CC:2C
```

### 🚀 Release Environment (الإنتاج)
**المصدر**: upload-keystore.jks
**الاستخدام**: flutter build apk --release, flutter build appbundle --release

```
SHA-1:   A8:1E:FC:57:F5:B0:31:19:96:39:60:40:BD:FE:A3:7C:CD:04:CD:8E
SHA-256: BB:5E:C7:D4:D2:C5:10:8F:AB:7A:18:28:15:92:1F:B9:C9:02:6E:98:91:FB:F3:55:F7:87
```

### 🏪 Play Store Environment (المتجر)
**المصدر**: Google Play Console App Signing
**الاستخدام**: Apps downloaded from Play Store

```
SHA-1:   3C:FD:A5:8A:3B:31:26:2B:BD:C1:AF:26:B8:5F:C2:84:DA:66:B4:67
SHA-256: 4D:81:30:A9:D7:80:18:D9:09:68:9D:7C:B6:8F:16:A6:96:20:B8:50:8D:C8:C8:E1:83:5D:66:84:A2:5E:FE:67
```

---

## 🔧 خطوات الإضافة في Firebase Console

### 1. الوصول إلى الإعدادات
1. [Firebase Console](https://console.firebase.google.com) → `test-5c820`
2. Project Settings (⚙️) → Your apps → Android app
3. SHA certificate fingerprints section

### 2. إضافة المفاتيح
انسخ والصق كل مفتاح من القائمة أعلاه:
- اضغط "Add fingerprint"
- الصق المفتاح
- اضغط "Save"
- كرر للمفاتيح الستة

### 3. تحميل التكوين الجديد
بعد إضافة جميع المفاتيح:
1. اضغط "Download google-services.json"
2. استبدل الملف في `android/app/google-services.json`

---

## ✅ التحقق من الإعداد

### اختبار Debug:
```bash
flutter run --debug
```

### اختبار Release:
```bash
flutter build apk --release
flutter install
```

### اختبار Play Store:
- رفع إلى Play Store
- تحميل من المتجر واختبار

---

## 🚨 ملاحظات مهمة

1. **جميع المفاتيح مطلوبة**: لا تحذف أي مفتاح
2. **انتظار التحديث**: قد يستغرق 5-10 دقائق
3. **تنظيف التطبيق**: امسح cache بعد التحديث
4. **اختبار شامل**: اختبر في جميع البيئات

---

---

## 🆔 Client IDs الجديدة المُنشأة

### Android Client IDs:
```
Debug:      545165014521-670ko5rl2799jf1iltuk2t6874mov5l2.apps.googleusercontent.com
Release:    545165014521-m0doh33j49vm6dhjl4t1vhbudn8ciahf.apps.googleusercontent.com
Play Store: 545165014521-q7v03d8o8qk4ugvk5tslb922ccfjjin5.apps.googleusercontent.com
```

### iOS Client ID:
```
iOS: 545165014521-6lga42ejvda9e36l607gkllobevvpjfn.apps.googleusercontent.com
```

---

## 📞 استكشاف الأخطاء

إذا لم يعمل Google Sign-In:
1. تأكد من إضافة جميع المفاتيح الستة
2. تأكد من Package Name: `com.busaty.school`
3. انتظر 10 دقائق للتحديث
4. امسح cache التطبيق
5. أعد تشغيل التطبيق
